{"mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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"}, "type": "stdio"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}