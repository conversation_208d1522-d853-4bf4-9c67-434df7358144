# AGENTS.md - Development Guidelines for Agentic Coding

## Build Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production  
- `pnpm preview` - Preview production build
- `pnpm check` - Run Astro type checking

## Code Style & Conventions
- **TypeScript**: Use strict mode, define interfaces for props, use Zod schemas for content collections
- **Imports**: Use `@/` path alias for src/, import components before utilities, group by type
- **Astro Components**: Use `.astro` extension, define Props interface in frontmatter, use TypeScript
- **Styling**: Use Tailwind CSS classes, follow responsive-first approach, use custom CSS variables from tailwind.config.mjs
- **Content**: Store in `src/content/` with proper Zod schemas, use MDX for rich content
- **Naming**: Use PascalCase for components, camelCase for variables, kebab-case for files/folders
- **Error Handling**: Use optional chaining, provide fallbacks for missing data, validate with Zod
- **Accessibility**: Include ARIA labels, semantic HTML, skip links, proper heading hierarchy
- **Performance**: Use preload for critical resources, optimize images with Sharp, lazy load non-critical assets

## Project Structure
- Components in `src/components/` (Header.astro, Footer.astro, etc.)
- Layouts in `src/layouts/` (Layout.astro base template)
- Content collections in `src/content/` with config.ts schemas
- Pages in `src/pages/` following Astro file-based routing
- Global styles in `src/styles/global.css`