# Portfolio Assets Structure Guide

## Directory Organization

The portfolio assets are organized in the following structure:

```
public/images/portfolio/
├── [project-slug]/
│   ├── hero.jpg              # Main hero image (1200x600px recommended)
│   ├── screenshots/          # Application screenshots
│   │   ├── desktop-1.jpg
│   │   ├── desktop-2.jpg
│   │   └── mobile-1.jpg
│   ├── architecture/         # System architecture diagrams
│   │   ├── overview.jpg
│   │   └── components.jpg
│   ├── ui/                   # UI/UX design images
│   │   ├── wireframes.jpg
│   │   └── mockups.jpg
│   └── demo/                 # Demo or workflow images
│       ├── workflow-1.jpg
│       └── workflow-2.jpg
```

## Image Categories

### 1. Hero Images
- **Purpose**: Main project showcase image
- **Dimensions**: 1200x600px (2:1 aspect ratio)
- **Format**: JPG or WebP
- **Naming**: `hero.jpg`

### 2. Screenshots
- **Purpose**: Application interface captures
- **Categories**: `desktop`, `mobile`, `tablet`
- **Dimensions**: Actual screen resolution or scaled proportionally
- **Format**: JPG or PNG
- **Naming**: `[category]-[number].jpg`

### 3. Architecture Diagrams
- **Purpose**: System architecture and technical diagrams
- **Dimensions**: 1000x800px minimum
- **Format**: JPG or PNG
- **Naming**: `[diagram-type].jpg`

### 4. UI/UX Images
- **Purpose**: Design process, wireframes, mockups
- **Dimensions**: Variable based on content
- **Format**: JPG or PNG
- **Naming**: `[design-stage].jpg`

### 5. Demo Images
- **Purpose**: Workflow demonstrations, feature highlights
- **Dimensions**: 800x600px minimum
- **Format**: JPG or PNG
- **Naming**: `[demo-type]-[number].jpg`

## Image Optimization Guidelines

### File Size
- Hero images: < 200KB
- Screenshots: < 150KB
- Diagrams: < 100KB
- UI images: < 100KB

### Quality
- Use 80-85% JPEG quality
- Optimize for web using tools like ImageOptim or TinyPNG
- Consider WebP format for better compression

### Accessibility
- Always include descriptive alt text
- Use high contrast for diagrams
- Ensure text in images is readable

## Content Schema Integration

The enhanced portfolio schema supports the following image structure:

```typescript
gallery: [
  {
    src: "/images/portfolio/project-slug/hero.jpg",
    alt: "Project hero image",
    caption: "Main application dashboard",
    category: "screenshot"
  },
  {
    src: "/images/portfolio/project-slug/architecture/overview.jpg",
    alt: "System architecture diagram",
    caption: "High-level system architecture",
    category: "architecture"
  }
]
```

## Placeholder Images

For projects without images, use placeholder images with the following specifications:
- Dimensions: Match category requirements
- Background: Gradient or solid color matching brand
- Text: Project name and category
- Format: SVG for scalability

## Adding New Project Images

1. Create project directory: `public/images/portfolio/[project-slug]/`
2. Add hero image: `hero.jpg`
3. Organize additional images by category
4. Update project MDX frontmatter with gallery array
5. Test image loading and gallery functionality

## Best Practices

1. **Consistent Naming**: Use kebab-case for all file names
2. **Responsive Images**: Provide multiple sizes when possible
3. **Lazy Loading**: Images are lazy-loaded by default
4. **SEO Optimization**: Include descriptive alt text and captions
5. **Performance**: Optimize all images before uploading
6. **Backup**: Keep original high-resolution versions separately

## Example Project Structure

```
public/images/portfolio/ecommerce-platform/
├── hero.jpg                    # Main showcase image
├── screenshots/
│   ├── desktop-dashboard.jpg   # Admin dashboard
│   ├── desktop-products.jpg    # Product catalog
│   ├── mobile-checkout.jpg     # Mobile checkout flow
│   └── mobile-cart.jpg         # Shopping cart
├── architecture/
│   ├── system-overview.jpg     # High-level architecture
│   ├── microservices.jpg       # Microservices diagram
│   └── database-schema.jpg     # Database design
├── ui/
│   ├── wireframes.jpg          # Initial wireframes
│   └── final-design.jpg        # Final UI design
└── demo/
    ├── user-journey.jpg        # User workflow
    └── admin-workflow.jpg      # Admin workflow
```

This structure ensures organized, scalable, and maintainable portfolio assets that integrate seamlessly with the enhanced portfolio system.
