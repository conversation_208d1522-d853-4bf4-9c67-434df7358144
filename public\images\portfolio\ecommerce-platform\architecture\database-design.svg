<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6eb5ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#001c90;stop-opacity:1" />
    </linearGradient>

    <!-- Overlay gradient for depth -->
    <radialGradient id="overlayGrad" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:white;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:white;stop-opacity:0" />
    </radialGradient>

    <!-- Text shadow filter -->
    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#mainGrad)"/>

  <!-- Overlay for depth -->
  <rect width="100%" height="100%" fill="url(#overlayGrad)"/>

  <!-- Geometric decorative elements -->
  <polygon points="0,0 120,0 0,120" fill="white" opacity="0.05"/>
  <polygon points="1000,800 880,800 1000,680" fill="white" opacity="0.05"/>

  <!-- Main Text -->
  <text x="50%" y="42%"
        text-anchor="middle"
        dominant-baseline="middle"
        fill="white"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="48"
        font-weight="700"
        filter="url(#textShadow)">
    Database Design
  </text>

  
  <!-- Subtitle -->
  <text x="50%" y="58%"
        text-anchor="middle"
        dominant-baseline="middle"
        fill="white"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="20"
        font-weight="400"
        opacity="0.95"
        filter="url(#textShadow)">
    Optimized Schema
  </text>
  

  <!-- Modern decorative elements -->
  <circle cx="15%" cy="25%" r="4" fill="white" opacity="0.2"/>
  <circle cx="85%" cy="75%" r="6" fill="white" opacity="0.15"/>
  <circle cx="90%" cy="20%" r="3" fill="white" opacity="0.25"/>
  <rect x="10%" y="80%" width="8" height="8" rx="2" fill="white" opacity="0.1"/>
  <rect x="85%" y="15%" width="6" height="6" rx="1" fill="white" opacity="0.2"/>
</svg>