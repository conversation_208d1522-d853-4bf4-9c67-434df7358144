<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-commerce Platform Placeholder Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .placeholder {
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .hero {
            width: 1200px;
            height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .screenshot {
            width: 800px;
            height: 600px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .architecture {
            width: 1000px;
            height: 800px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        h1 {
            font-size: 3rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        h2 {
            font-size: 2rem;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-commerce Platform Placeholder Images</h1>
        <p>Right-click on any image and "Save as" to download the placeholder image.</p>
        
        <!-- Hero Image -->
        <div class="placeholder">
            <div class="hero" id="hero">
                <div>
                    <h1>Enterprise E-commerce Platform</h1>
                    <div class="subtitle">Scalable • Secure • High-Performance</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('hero', 'hero.jpg')">Download Hero Image</button>
        </div>

        <!-- Admin Dashboard Screenshot -->
        <div class="placeholder">
            <div class="screenshot" id="admin-dashboard">
                <div>
                    <h2>Admin Dashboard</h2>
                    <div class="subtitle">Sales Analytics & Inventory Overview</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('admin-dashboard', 'admin-dashboard.jpg')">Download Screenshot</button>
        </div>

        <!-- Product Catalog Screenshot -->
        <div class="placeholder">
            <div class="screenshot" id="product-catalog" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div>
                    <h2>Product Catalog</h2>
                    <div class="subtitle">Advanced Search & Filtering</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('product-catalog', 'product-catalog.jpg')">Download Screenshot</button>
        </div>

        <!-- Checkout Flow Screenshot -->
        <div class="placeholder">
            <div class="screenshot" id="checkout-flow" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                <div>
                    <h2>Checkout Flow</h2>
                    <div class="subtitle">Streamlined Payment Process</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('checkout-flow', 'checkout-flow.jpg')">Download Screenshot</button>
        </div>

        <!-- System Architecture -->
        <div class="placeholder">
            <div class="architecture" id="system-overview">
                <div>
                    <h2>System Architecture</h2>
                    <div class="subtitle">Microservices • API Gateway • Service Mesh</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('system-overview', 'system-overview.jpg')">Download Architecture</button>
        </div>

        <!-- Database Design -->
        <div class="placeholder">
            <div class="architecture" id="database-design" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div>
                    <h2>Database Design</h2>
                    <div class="subtitle">Optimized Schema & Relationships</div>
                </div>
            </div>
            <button class="download-btn" onclick="downloadImage('database-design', 'database-design.jpg')">Download Database Design</button>
        </div>
    </div>

    <script>
        function downloadImage(elementId, filename) {
            const element = document.getElementById(elementId);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size to match element
            const rect = element.getBoundingClientRect();
            canvas.width = element.offsetWidth;
            canvas.height = element.offsetHeight;
            
            // Get computed styles
            const styles = window.getComputedStyle(element);
            const background = styles.background;
            
            // Fill background
            ctx.fillStyle = background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add text
            const text = element.querySelector('h1, h2').textContent;
            const subtitle = element.querySelector('.subtitle')?.textContent || '';
            
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.font = 'bold 48px Arial';
            ctx.fillText(text, canvas.width / 2, canvas.height / 2 - 20);
            
            if (subtitle) {
                ctx.font = '24px Arial';
                ctx.fillText(subtitle, canvas.width / 2, canvas.height / 2 + 30);
            }
            
            // Download
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
