// Simple script to generate SVG placeholder images for portfolio projects
// Run with: node scripts/generate-placeholder-images.cjs

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Modern cohesive color palette for professional look
const modernColors = {
  primary: '#3b82f6',      // Blue - Professional, trustworthy
  secondary: '#8b5cf6',    // Purple - Creative, innovative
  accent: '#06b6d4',       // Cyan - Modern, tech-focused
  success: '#10b981',      // Green - Growth, success
  warning: '#f59e0b',      // Amber - Energy, attention
  rose: '#f43f5e',         // Rose - Passion, dynamic
  slate: '#64748b',        // Slate - Professional, neutral
  indigo: '#6366f1'        // Indigo - Deep, sophisticated
};

// Project configurations with cohesive modern colors
const projects = {
  'ecommerce-platform': {
    title: 'Enterprise E-commerce Platform',
    color: modernColors.primary,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'E-commerce Platform', subtitle: 'Scalable • Secure • High-Performance' },
      { name: 'admin-dashboard.svg', width: 800, height: 600, text: 'Admin Dashboard', subtitle: 'Sales Analytics & Inventory' },
      { name: 'product-catalog.svg', width: 800, height: 600, text: 'Product Catalog', subtitle: 'Advanced Search & Filtering' },
      { name: 'checkout-flow.svg', width: 800, height: 600, text: 'Checkout Flow', subtitle: 'Streamlined Payment Process' },
      { name: 'system-overview.svg', width: 1000, height: 800, text: 'System Architecture', subtitle: 'Microservices Architecture' },
      { name: 'database-design.svg', width: 1000, height: 800, text: 'Database Design', subtitle: 'Optimized Schema' }
    ]
  },
  'hrms-platform': {
    title: 'HRMS Platform',
    color: modernColors.success,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'HRMS Platform', subtitle: 'Human Resource Management' }
    ]
  },
  'insurance-broker-platform': {
    title: 'Insurance Broker Platform',
    color: modernColors.indigo,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'Insurance Broker Platform', subtitle: 'Policy Management System' }
    ]
  },
  'insurance-system': {
    title: 'Insurance System',
    color: modernColors.accent,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'Insurance System', subtitle: 'Comprehensive Insurance Solution' }
    ]
  },
  'mydestiny-astrology-platform': {
    title: 'MyDestiny Astrology Platform',
    color: modernColors.secondary,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'MyDestiny Platform', subtitle: 'Astrology & Predictions' }
    ]
  },
  'real-estate-platform': {
    title: 'Real Estate Platform',
    color: modernColors.warning,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'Real Estate Platform', subtitle: 'Property Management System' }
    ]
  },
  'core-backend-system': {
    title: 'Core Backend System',
    color: modernColors.slate,
    images: [
      { name: 'hero.svg', width: 1200, height: 600, text: 'Core Backend System', subtitle: 'Scalable Infrastructure' }
    ]
  }
};

function generateSVG(config) {
  const { width, height, text, subtitle, color } = config;

  // Create a more sophisticated gradient with multiple stops
  const darkerColor = adjustColor(color, -40);
  const lighterColor = adjustColor(color, 20);

  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${lighterColor};stop-opacity:1" />
      <stop offset="50%" style="stop-color:${color};stop-opacity:1" />
      <stop offset="100%" style="stop-color:${darkerColor};stop-opacity:1" />
    </linearGradient>

    <!-- Overlay gradient for depth -->
    <radialGradient id="overlayGrad" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:white;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:white;stop-opacity:0" />
    </radialGradient>

    <!-- Text shadow filter -->
    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#mainGrad)"/>

  <!-- Overlay for depth -->
  <rect width="100%" height="100%" fill="url(#overlayGrad)"/>

  <!-- Geometric decorative elements -->
  <polygon points="0,0 120,0 0,120" fill="white" opacity="0.05"/>
  <polygon points="${width},${height} ${width-120},${height} ${width},${height-120}" fill="white" opacity="0.05"/>

  <!-- Main Text -->
  <text x="50%" y="42%"
        text-anchor="middle"
        dominant-baseline="middle"
        fill="white"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="${Math.min(width/20, 48)}"
        font-weight="700"
        filter="url(#textShadow)">
    ${text}
  </text>

  ${subtitle ? `
  <!-- Subtitle -->
  <text x="50%" y="58%"
        text-anchor="middle"
        dominant-baseline="middle"
        fill="white"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="${Math.min(width/50, 20)}"
        font-weight="400"
        opacity="0.95"
        filter="url(#textShadow)">
    ${subtitle}
  </text>
  ` : ''}

  <!-- Modern decorative elements -->
  <circle cx="15%" cy="25%" r="4" fill="white" opacity="0.2"/>
  <circle cx="85%" cy="75%" r="6" fill="white" opacity="0.15"/>
  <circle cx="90%" cy="20%" r="3" fill="white" opacity="0.25"/>
  <rect x="10%" y="80%" width="8" height="8" rx="2" fill="white" opacity="0.1"/>
  <rect x="85%" y="15%" width="6" height="6" rx="1" fill="white" opacity="0.2"/>
</svg>`;
}

function adjustColor(color, amount) {
  // Simple color adjustment function
  const num = parseInt(color.replace("#", ""), 16);
  const amt = Math.round(2.55 * amount);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Generate images for all projects
Object.entries(projects).forEach(([projectSlug, projectConfig]) => {
  const projectDir = path.join(__dirname, '..', 'public', 'images', 'portfolio', projectSlug);
  ensureDirectoryExists(projectDir);
  
  // Create subdirectories
  ensureDirectoryExists(path.join(projectDir, 'screenshots'));
  ensureDirectoryExists(path.join(projectDir, 'architecture'));
  
  projectConfig.images.forEach(imageConfig => {
    const config = {
      ...imageConfig,
      color: projectConfig.color
    };
    
    const svg = generateSVG(config);
    
    // Determine subdirectory based on image name
    let subDir = '';
    if (imageConfig.name.includes('dashboard') || imageConfig.name.includes('catalog') || imageConfig.name.includes('checkout')) {
      subDir = 'screenshots';
    } else if (imageConfig.name.includes('system') || imageConfig.name.includes('database') || imageConfig.name.includes('architecture')) {
      subDir = 'architecture';
    }
    
    const filePath = path.join(projectDir, subDir, imageConfig.name);
    
    fs.writeFileSync(filePath, svg);
    console.log(`Generated: ${filePath}`);
  });
});

console.log('Placeholder images generated successfully!');
console.log('Note: These are SVG placeholders. For production, replace with actual project screenshots and diagrams.');
