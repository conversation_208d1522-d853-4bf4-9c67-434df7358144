---
interface GalleryImage {
  src: string;
  alt: string;
  caption?: string;
  category?: 'screenshot' | 'diagram' | 'demo' | 'architecture' | 'ui' | 'mobile';
}

interface Props {
  images: GalleryImage[];
  title?: string;
  className?: string;
}

const { images, title, className = '' } = Astro.props;

// Generate unique ID for this gallery instance
const galleryId = `gallery-${Math.random().toString(36).substr(2, 9)}`;
---

<div class={`image-gallery ${className}`} data-gallery-id={galleryId}>
  {title && (
    <h3 class="text-2xl font-bold text-secondary mb-6 font-heading">
      {title}
    </h3>
  )}
  
  <!-- Gallery Grid -->
  <div class="gallery-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
    {images.map((image, index) => (
      <div 
        class="gallery-item group relative overflow-hidden rounded-xl bg-gray-100 aspect-video cursor-pointer hover:shadow-lg transition-all duration-300"
        data-index={index}
        data-src={image.src}
        data-alt={image.alt}
        data-caption={image.caption}
        data-category={image.category}
      >
        <img 
          src={image.src} 
          alt={image.alt}
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
        
        <!-- Overlay -->
        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
          <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
            </svg>
          </div>
        </div>
        
        <!-- Category Badge -->
        {image.category && (
          <div class="absolute top-2 left-2 px-2 py-1 bg-black/70 text-white text-xs rounded-md font-medium">
            {image.category}
          </div>
        )}
      </div>
    ))}
  </div>
  
  <!-- Lightbox Modal -->
  <div 
    id={`lightbox-${galleryId}`}
    class="lightbox fixed inset-0 bg-black/90 z-50 hidden items-center justify-center p-4"
    role="dialog"
    aria-modal="true"
    aria-labelledby={`lightbox-title-${galleryId}`}
  >
    <!-- Close Button -->
    <button 
      class="lightbox-close absolute top-4 right-4 text-white hover:text-gray-300 z-10"
      aria-label="Close gallery"
    >
      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
    
    <!-- Navigation Buttons -->
    <button 
      class="lightbox-prev absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
      aria-label="Previous image"
    >
      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>
    
    <button 
      class="lightbox-next absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
      aria-label="Next image"
    >
      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>
    
    <!-- Image Container -->
    <div class="lightbox-content max-w-4xl max-h-full flex flex-col items-center">
      <img 
        class="lightbox-image max-w-full max-h-[80vh] object-contain"
        src=""
        alt=""
      />
      
      <!-- Image Info -->
      <div class="lightbox-info mt-4 text-center text-white max-w-2xl">
        <h4 id={`lightbox-title-${galleryId}`} class="lightbox-caption text-lg font-semibold mb-2"></h4>
        <p class="lightbox-counter text-sm text-gray-300"></p>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ galleryId }}>
  // Gallery functionality
  document.addEventListener('DOMContentLoaded', function() {
    const gallery = document.querySelector(`[data-gallery-id="${galleryId}"]`);
    if (!gallery) return;
    
    const lightbox = document.getElementById(`lightbox-${galleryId}`);
    const lightboxImage = lightbox.querySelector('.lightbox-image');
    const lightboxCaption = lightbox.querySelector('.lightbox-caption');
    const lightboxCounter = lightbox.querySelector('.lightbox-counter');
    const closeBtn = lightbox.querySelector('.lightbox-close');
    const prevBtn = lightbox.querySelector('.lightbox-prev');
    const nextBtn = lightbox.querySelector('.lightbox-next');
    const galleryItems = gallery.querySelectorAll('.gallery-item');
    
    let currentIndex = 0;
    let images = [];
    
    // Initialize images array
    galleryItems.forEach((item, index) => {
      images.push({
        src: item.dataset.src,
        alt: item.dataset.alt,
        caption: item.dataset.caption || '',
        category: item.dataset.category || ''
      });
      
      // Add click listener
      item.addEventListener('click', () => openLightbox(index));
    });
    
    function openLightbox(index) {
      currentIndex = index;
      updateLightboxContent();
      lightbox.classList.remove('hidden');
      lightbox.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }
    
    function closeLightbox() {
      lightbox.classList.add('hidden');
      lightbox.classList.remove('flex');
      document.body.style.overflow = '';
    }
    
    function updateLightboxContent() {
      const image = images[currentIndex];
      lightboxImage.src = image.src;
      lightboxImage.alt = image.alt;
      lightboxCaption.textContent = image.caption || image.alt;
      lightboxCounter.textContent = `${currentIndex + 1} of ${images.length}`;
    }
    
    function nextImage() {
      currentIndex = (currentIndex + 1) % images.length;
      updateLightboxContent();
    }
    
    function prevImage() {
      currentIndex = (currentIndex - 1 + images.length) % images.length;
      updateLightboxContent();
    }
    
    // Event listeners
    closeBtn.addEventListener('click', closeLightbox);
    nextBtn.addEventListener('click', nextImage);
    prevBtn.addEventListener('click', prevImage);
    
    // Close on background click
    lightbox.addEventListener('click', (e) => {
      if (e.target === lightbox) closeLightbox();
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (!lightbox.classList.contains('hidden')) {
        switch(e.key) {
          case 'Escape':
            closeLightbox();
            break;
          case 'ArrowLeft':
            prevImage();
            break;
          case 'ArrowRight':
            nextImage();
            break;
        }
      }
    });
  });
</script>

<style>
  .gallery-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .gallery-item:hover {
    transform: translateY(-2px);
  }
  
  .lightbox {
    backdrop-filter: blur(4px);
  }
  
  .lightbox-image {
    transition: opacity 0.3s ease;
  }
  
  @media (max-width: 640px) {
    .gallery-grid {
      grid-template-columns: 1fr;
    }
    
    .lightbox-prev,
    .lightbox-next {
      display: none;
    }
  }
</style>
