---
// MagneticCursor component for enhanced interactive cursor effects
---

<div id="cursor" class="cursor-dot"></div>
<div id="cursor-trail" class="cursor-trail"></div>

<script>
  class MagneticCursor {
    private cursor: HTMLElement | null = null;
    private trail: HTMLElement | null = null;
    private isActive = false;
    private mouseX = 0;
    private mouseY = 0;
    private trailX = 0;
    private trailY = 0;
    private magneticElements: NodeListOf<Element>;

    constructor() {
      this.cursor = document.getElementById('cursor');
      this.trail = document.getElementById('cursor-trail');
      
      // Only initialize on desktop devices
      if (this.isDesktop()) {
        this.magneticElements = document.querySelectorAll('.magnetic');
        this.bindEvents();
        this.animate();
      } else {
        this.hide();
      }
    }

    private isDesktop(): boolean {
      return window.innerWidth >= 1024 && !('ontouchstart' in window);
    }

    private hide() {
      if (this.cursor) this.cursor.style.display = 'none';
      if (this.trail) this.trail.style.display = 'none';
    }

    private bindEvents() {
      document.addEventListener('mousemove', (e) => {
        this.mouseX = e.clientX;
        this.mouseY = e.clientY;
        this.updateCursor();
      });

      document.addEventListener('mouseenter', () => {
        this.isActive = true;
        this.show();
      });

      document.addEventListener('mouseleave', () => {
        this.isActive = false;
        this.hide();
      });

      // Handle magnetic elements
      this.magneticElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
          this.expandCursor();
        });

        element.addEventListener('mouseleave', () => {
          this.resetCursor();
          this.resetElement(element as HTMLElement);
        });

        element.addEventListener('mousemove', (e) => {
          this.handleMagneticEffect(e as MouseEvent, element as HTMLElement);
        });
      });

      // Handle click elements
      document.addEventListener('mousedown', () => {
        this.scaleCursor(0.8);
      });

      document.addEventListener('mouseup', () => {
        this.scaleCursor(1);
      });

      // Handle links and buttons
      const interactiveElements = document.querySelectorAll('a, button, [role="button"]');
      interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
          this.expandCursor();
        });

        element.addEventListener('mouseleave', () => {
          this.resetCursor();
        });
      });
    }

    private updateCursor() {
      if (!this.cursor || !this.isActive) return;

      this.cursor.style.transform = `translate3d(${this.mouseX}px, ${this.mouseY}px, 0)`;
    }

    private animate() {
      if (!this.trail || !this.isActive) {
        requestAnimationFrame(() => this.animate());
        return;
      }

      // Smooth trail animation
      this.trailX += (this.mouseX - this.trailX) * 0.1;
      this.trailY += (this.mouseY - this.trailY) * 0.1;

      this.trail.style.transform = `translate3d(${this.trailX}px, ${this.trailY}px, 0)`;

      requestAnimationFrame(() => this.animate());
    }

    private show() {
      if (this.cursor) {
        this.cursor.style.display = 'block';
        this.cursor.style.opacity = '1';
      }
      if (this.trail) {
        this.trail.style.display = 'block';
        this.trail.style.opacity = '1';
      }
    }

    private expandCursor() {
      if (!this.cursor) return;
      
      this.cursor.style.transform += ' scale(1.5)';
      this.cursor.style.backgroundColor = 'rgba(59, 130, 246, 0.8)';
      this.cursor.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.6)';
    }

    private resetCursor() {
      if (!this.cursor) return;
      
      this.cursor.style.transform = this.cursor.style.transform.replace(' scale(1.5)', '');
      this.cursor.style.backgroundColor = '';
      this.cursor.style.boxShadow = '';
    }

    private scaleCursor(scale: number) {
      if (!this.cursor) return;
      
      this.cursor.style.transform += ` scale(${scale})`;
      setTimeout(() => {
        if (this.cursor) {
          this.cursor.style.transform = this.cursor.style.transform.replace(` scale(${scale})`, '');
        }
      }, 150);
    }

    private handleMagneticEffect(e: MouseEvent, element: HTMLElement) {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = e.clientX - centerX;
      const deltaY = e.clientY - centerY;
      
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const maxDistance = Math.min(rect.width, rect.height);
      
      if (distance < maxDistance) {
        const strength = Math.min(distance / maxDistance, 1);
        const translateX = deltaX * strength * 0.3;
        const translateY = deltaY * strength * 0.3;
        
        element.style.transform = `translate3d(${translateX}px, ${translateY}px, 0)`;
      }
    }

    private resetElement(element: HTMLElement) {
      element.style.transform = '';
    }

    public destroy() {
      if (this.cursor) this.cursor.remove();
      if (this.trail) this.trail.remove();
    }
  }

  // Initialize magnetic cursor
  function initMagneticCursor() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (!prefersReducedMotion) {
      new MagneticCursor();
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMagneticCursor);
  } else {
    initMagneticCursor();
  }

  // Re-initialize on page navigation (for Astro view transitions)
  document.addEventListener('astro:page-load', initMagneticCursor);
</script>

<style>
  .cursor-dot {
    position: fixed;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.15s ease-out;
    transform: translate(-50%, -50%);
    opacity: 0;
    mix-blend-mode: difference;
    display: none;
  }

  .cursor-trail {
    position: fixed;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    border: 2px solid rgba(59, 130, 246, 0.5);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.2s ease-out;
    display: none;
  }

  /* Dark mode styles */
  .dark .cursor-dot {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    mix-blend-mode: screen;
  }

  .dark .cursor-trail {
    border-color: rgba(96, 165, 250, 0.6);
  }

  /* Hide default cursor on magnetic elements when custom cursor is active */
  @media (min-width: 1024px) and (hover: hover) {
    .magnetic:hover,
    a:hover,
    button:hover,
    [role="button"]:hover {
      cursor: none;
    }

    body:hover .cursor-dot,
    body:hover .cursor-trail {
      opacity: 1;
    }
  }

  /* Hide on mobile and touch devices */
  @media (max-width: 1023px), (hover: none) {
    .cursor-dot,
    .cursor-trail {
      display: none !important;
    }
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .cursor-dot,
    .cursor-trail {
      display: none !important;
    }

    .magnetic {
      transform: none !important;
      transition: none !important;
    }
  }

  /* Enhanced magnetic effect for better visual feedback */
  .magnetic {
    transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    will-change: transform;
  }

  /* Smooth transitions for all interactive elements */
  a, button, [role="button"] {
    transition: all 0.3s ease;
  }
</style>