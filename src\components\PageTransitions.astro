---
// PageTransitions component for smooth loading states and transitions
---

<script>
  class PageTransitions {
    private isFirstLoad = true;

    constructor() {
      this.initializePageTransitions();
      this.bindEvents();
    }

    private initializePageTransitions() {
      // Add initial loading state
      document.body.classList.add('page-loading');
      
      // Remove loading state once everything is ready
      this.onPageReady(() => {
        document.body.classList.remove('page-loading');
        document.body.classList.add('page-loaded');
        
        // Trigger content animations after a short delay
        setTimeout(() => {
          this.triggerContentAnimations();
        }, 100);
      });
    }

    private onPageReady(callback: () => void) {
      if (document.readyState === 'complete') {
        // Page is already loaded
        setTimeout(callback, this.isFirstLoad ? 500 : 100);
      } else {
        // Wait for page to load
        window.addEventListener('load', () => {
          setTimeout(callback, this.isFirstLoad ? 500 : 100);
        });
      }
      
      this.isFirstLoad = false;
    }

    private triggerContentAnimations() {
      // Add stagger animation classes to elements
      const staggerContainers = document.querySelectorAll('.stagger-content');
      staggerContainers.forEach(container => {
        const children = Array.from(container.children);
        children.forEach((child, index) => {
          setTimeout(() => {
            child.classList.add('content-fade-in');
          }, index * 100);
        });
      });

      // Trigger individual content fade-ins
      const fadeElements = document.querySelectorAll('.content-fade-in');
      fadeElements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('content-fade-in');
        }, index * 50);
      });
    }

    private bindEvents() {
      // Handle form submissions
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        form.addEventListener('submit', (e) => {
          const submitButton = form.querySelector('button[type="submit"], input[type="submit"]') as HTMLElement;
          if (submitButton) {
            this.showButtonLoading(submitButton);
          }
        });
      });

      // Handle link navigation with loading states
      const links = document.querySelectorAll('a[href^="/"]');
      links.forEach(link => {
        link.addEventListener('click', (e) => {
          const href = (link as HTMLAnchorElement).href;
          if (href !== window.location.href) {
            this.showNavigationLoading();
          }
        });
      });

      // Handle Astro view transitions
      document.addEventListener('astro:before-preparation', () => {
        this.showPageTransition();
      });

      document.addEventListener('astro:after-swap', () => {
        this.hidePageTransition();
        this.triggerContentAnimations();
      });
    }

    private showButtonLoading(button: HTMLElement) {
      const originalContent = button.innerHTML;
      const loadingSpinner = '<div class="spinner"></div>';
      
      button.innerHTML = loadingSpinner;
      button.setAttribute('disabled', 'true');
      
      // Store original content for restoration
      button.setAttribute('data-original-content', originalContent);
      
      // Auto-restore after timeout (fallback)
      setTimeout(() => {
        this.hideButtonLoading(button);
      }, 10000);
    }

    private hideButtonLoading(button: HTMLElement) {
      const originalContent = button.getAttribute('data-original-content');
      if (originalContent) {
        button.innerHTML = originalContent;
        button.removeAttribute('disabled');
        button.removeAttribute('data-original-content');
      }
    }

    private showNavigationLoading() {
      // Add a subtle loading indicator
      const loader = document.createElement('div');
      loader.id = 'navigation-loader';
      loader.className = 'fixed top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-600 to-accent-600 z-50 transform origin-left scale-x-0 transition-transform duration-300';
      document.body.appendChild(loader);
      
      // Animate the loader
      setTimeout(() => {
        loader.style.transform = 'scaleX(1)';
      }, 10);
    }

    private hideNavigationLoading() {
      const loader = document.getElementById('navigation-loader');
      if (loader) {
        loader.style.transform = 'scaleX(1)';
        setTimeout(() => {
          loader.remove();
        }, 300);
      }
    }

    private showPageTransition() {
      document.body.classList.add('page-transition-exit');
      
      setTimeout(() => {
        document.body.classList.add('page-transition-exit-active');
      }, 10);
    }

    private hidePageTransition() {
      document.body.classList.remove('page-transition-exit', 'page-transition-exit-active');
      document.body.classList.add('page-transition-enter');
      
      setTimeout(() => {
        document.body.classList.add('page-transition-enter-active');
        
        setTimeout(() => {
          document.body.classList.remove('page-transition-enter', 'page-transition-enter-active');
        }, 300);
      }, 10);
    }

    // Image lazy loading with skeleton states
    public initializeLazyImages() {
      const images = document.querySelectorAll('img[data-src]');
      
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              this.loadImage(img);
              imageObserver.unobserve(img);
            }
          });
        });

        images.forEach(img => imageObserver.observe(img));
      } else {
        // Fallback for older browsers
        images.forEach(img => this.loadImage(img as HTMLImageElement));
      }
    }

    private loadImage(img: HTMLImageElement) {
      const placeholder = img.previousElementSibling;
      
      img.addEventListener('load', () => {
        img.classList.add('loaded');
        if (placeholder && placeholder.classList.contains('skeleton')) {
          placeholder.remove();
        }
      });

      img.addEventListener('error', () => {
        if (placeholder && placeholder.classList.contains('skeleton')) {
          placeholder.textContent = 'Image failed to load';
          placeholder.classList.remove('skeleton');
        }
      });

      img.src = img.getAttribute('data-src') || '';
      img.removeAttribute('data-src');
    }
  }

  // Initialize page transitions
  function initPageTransitions() {
    const pageTransitions = new PageTransitions();
    
    // Initialize lazy loading
    pageTransitions.initializeLazyImages();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageTransitions);
  } else {
    initPageTransitions();
  }

  // Re-initialize on page navigation (for Astro view transitions)
  document.addEventListener('astro:page-load', initPageTransitions);
</script>

<style>
  /* Loading indicator styles */
  #navigation-loader {
    background: linear-gradient(
      90deg,
      transparent,
      currentColor 50%,
      transparent
    );
    background-size: 200% 100%;
    animation: loading-slide 1s ease-in-out infinite;
  }

  @keyframes loading-slide {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Image loading states */
  img[data-src] {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  img[data-src].loaded {
    opacity: 1;
  }

  /* Skeleton image placeholder */
  .skeleton-image {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 25%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
  }

  .dark .skeleton-image {
    background: linear-gradient(
      90deg,
      rgba(15, 23, 42, 0.3) 25%,
      rgba(15, 23, 42, 0.5) 50%,
      rgba(15, 23, 42, 0.3) 75%
    );
    background-size: 200% 100%;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
  }
</style>