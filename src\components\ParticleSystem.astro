---
// ParticleSystem component for animated background particles
---

<div id="particle-container" class="fixed inset-0 pointer-events-none z-0 overflow-hidden"></div>

<script>
  class ParticleSystem {
    private canvas: HTMLCanvasElement;
    private ctx: CanvasRenderingContext2D;
    private particles: Particle[] = [];
    private animationId: number = 0;
    private mouse = { x: 0, y: 0, radius: 150 };
    private isDarkMode = false;
    private connectionDistance = 120;
    private maxParticles = 50;

    constructor() {
      this.createCanvas();
      this.initializeParticles();
      this.bindEvents();
      this.animate();
      this.checkDarkMode();
    }

    private createCanvas() {
      this.canvas = document.createElement('canvas');
      this.canvas.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
        opacity: 0.6;
      `;
      
      this.ctx = this.canvas.getContext('2d')!;
      this.resize();
      
      const container = document.getElementById('particle-container');
      if (container) {
        container.appendChild(this.canvas);
      }
    }

    private initializeParticles() {
      this.particles = [];
      const particleCount = Math.min(this.maxParticles, Math.floor((window.innerWidth * window.innerHeight) / 20000));
      
      for (let i = 0; i < particleCount; i++) {
        this.particles.push(new Particle(this.canvas.width, this.canvas.height));
      }
    }

    private bindEvents() {
      window.addEventListener('resize', () => this.resize());
      window.addEventListener('mousemove', (e) => {
        this.mouse.x = e.clientX;
        this.mouse.y = e.clientY;
      });

      // Dark mode detection
      const observer = new MutationObserver(() => {
        this.checkDarkMode();
      });

      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
    }

    private checkDarkMode() {
      this.isDarkMode = document.documentElement.classList.contains('dark');
    }

    private resize() {
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;
      this.initializeParticles();
    }

    private animate() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // Update and draw particles
      this.particles.forEach((particle, i) => {
        particle.update(this.canvas.width, this.canvas.height, this.mouse);
        particle.draw(this.ctx, this.isDarkMode);

        // Draw connections
        for (let j = i + 1; j < this.particles.length; j++) {
          const dx = particle.x - this.particles[j].x;
          const dy = particle.y - this.particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < this.connectionDistance) {
            const opacity = 1 - (distance / this.connectionDistance);
            this.drawConnection(particle, this.particles[j], opacity);
          }
        }
      });

      this.animationId = requestAnimationFrame(() => this.animate());
    }

    private drawConnection(p1: Particle, p2: Particle, opacity: number) {
      const gradient = this.ctx.createLinearGradient(p1.x, p1.y, p2.x, p2.y);
      
      if (this.isDarkMode) {
        gradient.addColorStop(0, `rgba(59, 130, 246, ${opacity * 0.3})`);
        gradient.addColorStop(1, `rgba(147, 197, 253, ${opacity * 0.2})`);
      } else {
        gradient.addColorStop(0, `rgba(59, 130, 246, ${opacity * 0.2})`);
        gradient.addColorStop(1, `rgba(79, 70, 229, ${opacity * 0.15})`);
      }

      this.ctx.strokeStyle = gradient;
      this.ctx.lineWidth = 1;
      this.ctx.beginPath();
      this.ctx.moveTo(p1.x, p1.y);
      this.ctx.lineTo(p2.x, p2.y);
      this.ctx.stroke();
    }

    public destroy() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
      }
      if (this.canvas && this.canvas.parentNode) {
        this.canvas.parentNode.removeChild(this.canvas);
      }
    }
  }

  class Particle {
    public x: number;
    public y: number;
    private vx: number;
    private vy: number;
    private size: number;
    private baseSize: number;
    private color: string;
    private pulseSpeed: number;
    private pulseOffset: number;

    constructor(canvasWidth: number, canvasHeight: number) {
      this.x = Math.random() * canvasWidth;
      this.y = Math.random() * canvasHeight;
      this.vx = (Math.random() - 0.5) * 0.8;
      this.vy = (Math.random() - 0.5) * 0.8;
      this.baseSize = Math.random() * 3 + 1;
      this.size = this.baseSize;
      this.pulseSpeed = Math.random() * 0.02 + 0.01;
      this.pulseOffset = Math.random() * Math.PI * 2;
      this.color = this.getRandomColor();
    }

    private getRandomColor(): string {
      const colors = [
        '59, 130, 246',   // Blue
        '147, 197, 253',  // Light Blue
        '79, 70, 229',    // Indigo
        '139, 92, 246',   // Purple
        '236, 72, 153',   // Pink
        '251, 146, 60',   // Orange
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    }

    update(canvasWidth: number, canvasHeight: number, mouse: { x: number; y: number; radius: number }) {
      // Mouse interaction
      const dx = mouse.x - this.x;
      const dy = mouse.y - this.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < mouse.radius) {
        const force = (mouse.radius - distance) / mouse.radius;
        const angle = Math.atan2(dy, dx);
        this.vx -= Math.cos(angle) * force * 0.5;
        this.vy -= Math.sin(angle) * force * 0.5;
      }

      // Update position
      this.x += this.vx;
      this.y += this.vy;

      // Bounce off edges
      if (this.x < 0 || this.x > canvasWidth) this.vx *= -0.9;
      if (this.y < 0 || this.y > canvasHeight) this.vy *= -0.9;

      // Keep within bounds
      this.x = Math.max(0, Math.min(canvasWidth, this.x));
      this.y = Math.max(0, Math.min(canvasHeight, this.y));

      // Apply friction
      this.vx *= 0.99;
      this.vy *= 0.99;

      // Pulse effect
      this.size = this.baseSize + Math.sin(Date.now() * this.pulseSpeed + this.pulseOffset) * 0.5;
    }

    draw(ctx: CanvasRenderingContext2D, isDarkMode: boolean) {
      const opacity = isDarkMode ? 0.8 : 0.6;
      
      // Create radial gradient for glow effect
      const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size * 3);
      gradient.addColorStop(0, `rgba(${this.color}, ${opacity})`);
      gradient.addColorStop(0.5, `rgba(${this.color}, ${opacity * 0.5})`);
      gradient.addColorStop(1, `rgba(${this.color}, 0)`);

      // Draw glow
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size * 3, 0, Math.PI * 2);
      ctx.fill();

      // Draw core particle
      ctx.fillStyle = `rgba(${this.color}, ${opacity})`;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  // Initialize particle system
  function initParticleSystem() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (!prefersReducedMotion) {
      // Only initialize on larger screens for performance
      if (window.innerWidth > 768) {
        new ParticleSystem();
      }
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initParticleSystem);
  } else {
    initParticleSystem();
  }

  // Re-initialize on page navigation (for Astro view transitions)
  document.addEventListener('astro:page-load', initParticleSystem);
</script>

<style>
  #particle-container {
    contain: layout style paint;
  }

  /* Disable particles on mobile for performance */
  @media (max-width: 768px) {
    #particle-container {
      display: none;
    }
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    #particle-container {
      display: none !important;
    }
  }
</style>