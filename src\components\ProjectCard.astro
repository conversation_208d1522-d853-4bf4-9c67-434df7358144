---
import { formatTechName, getTechBadgeClasses, sortTechnologies, getTechIcon } from '../utils/techUtils.js';

interface Props {
  title: string;
  description: string;
  tags: string[];
  heroImage?: string;
  featured?: boolean;
}

const {
  title,
  description,
  tags,
  heroImage,
  featured = false
} = Astro.props;

// Use the centralized sorting logic
const sortedTags = sortTechnologies(tags);
---

<article class={`group relative glass-card shadow-glass dark:shadow-glass-dark rounded-3xl overflow-hidden hover:shadow-neon hover:scale-105 magnetic transition-all duration-500 h-[500px] flex flex-col ${featured ? 'ring-2 ring-primary-500/30 shadow-neon' : ''}`} role="listitem">
  <!-- Hero Image -->
  {heroImage && (
    <div class="relative h-48 overflow-hidden flex-shrink-0">
      <img
        src={heroImage}
        alt={`${title} preview`}
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
    </div>
  )}

  <!-- Enhanced Gradient overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-500/8 via-transparent to-secondary-500/8 dark:from-primary-400/12 dark:via-transparent dark:to-secondary-400/12 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

  <!-- Minimal Badge Container -->
  <div class="absolute top-6 left-6 z-10">
    <!-- Enhanced Featured badge -->
    {featured && (
      <div class="px-4 py-2 bg-gradient-to-r from-accent-400 via-accent-500 to-accent-600 text-white text-xs font-bold rounded-full shadow-neon backdrop-blur-sm animate-pulse-glow">
        ⭐ Featured
      </div>
    )}
  </div>

  <!-- Enhanced Action indicator -->
  <div class="absolute top-6 right-6 z-10 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-3 group-hover:translate-x-0">
    <div class="w-12 h-12 glass-effect rounded-2xl flex items-center justify-center shadow-glass dark:shadow-glass-dark magnetic">
      <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 p-6 flex flex-col flex-grow">
    <!-- Header spacing for badges (adjust based on hero image) -->
    <div class={heroImage ? "h-2 mb-3" : "h-6 mb-3"}></div>

    <!-- Main content area with consistent spacing -->
    <div class="flex flex-col flex-grow">
      <h3 class="text-xl font-bold text-secondary-800 dark:text-secondary-100 mb-3 font-heading group-hover:text-primary-700 dark:group-hover:text-primary-400 transition-colors duration-300 line-clamp-2">
        {title}
      </h3>
      <div class="flex-grow mb-4">
        <p class="text-secondary-600 dark:text-secondary-300 leading-relaxed text-sm line-clamp-3">
          {description}
        </p>
      </div>
    </div>

    <!-- Enhanced tags - Fixed position at bottom -->
    <div class="flex flex-wrap gap-1.5 mt-auto pt-2 tech-badges-container" style="height: 3rem; overflow: hidden;">
      {sortedTags.slice(0, 6).map((tag, tagIndex) => {
        const displayTag = formatTechName(tag);
        const icon = getTechIcon(tag);

        return (
          <span
            class={getTechBadgeClasses(tag)}
            style={`animation-delay: ${tagIndex * 100}ms`}
            title={tag !== displayTag ? tag : undefined}
          >
            {icon && <span class="mr-1 text-xs">{icon}</span>}
            {displayTag}
          </span>
        );
      })}
      {tags.length > 6 && (
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 backdrop-blur-sm border bg-gradient-to-r from-slate-100 to-gray-100 dark:from-slate-600 dark:to-slate-700 text-slate-600 dark:text-slate-300 border-slate-200/50 dark:border-slate-600/50 tech-badge-enhanced">
          +{tags.length - 6}
        </span>
      )}
    </div>

    <!-- Enhanced Hover effect line -->
    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-accent-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left shadow-neon"></div>
  </div>
</article>