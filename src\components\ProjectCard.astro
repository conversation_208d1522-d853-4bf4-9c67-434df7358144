---
interface Props {
  title: string;
  description: string;
  tags: string[];
  heroImage?: string;
  featured?: boolean;
}

const {
  title,
  description,
  tags,
  heroImage,
  featured = false
} = Astro.props;

// Smart sorting: prioritize shorter technology names first
const sortedTags = [...tags].sort((a, b) => {
  // Prioritize common short technologies
  const shortTechs = ['Java', 'MySQL', 'JWT', 'REST', 'API'];
  const aIsShort = shortTechs.includes(a) || a.length <= 6;
  const bIsShort = shortTechs.includes(b) || b.length <= 6;

  if (aIsShort && !bIsShort) return -1;
  if (!aIsShort && bIsShort) return 1;

  // If both are short or both are long, sort by length
  return a.length - b.length;
});
---

<article class={`group relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-3xl shadow-lg border border-slate-200/50 dark:border-slate-700/50 overflow-hidden hover:shadow-2xl hover:scale-105 transition-all duration-500 hover:bg-white dark:hover:bg-slate-700 h-[500px] flex flex-col ${featured ? 'ring-2 ring-blue-500/20' : ''}`} role="listitem">
  <!-- Hero Image -->
  {heroImage && (
    <div class="relative h-48 overflow-hidden flex-shrink-0">
      <img
        src={heroImage}
        alt={`${title} preview`}
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
    </div>
  )}

  <!-- Gradient overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5 dark:from-blue-400/10 dark:via-transparent dark:to-indigo-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

  <!-- Minimal Badge Container -->
  <div class="absolute top-6 left-6 z-10">
    <!-- Featured badge only -->
    {featured && (
      <div class="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold rounded-full shadow-lg backdrop-blur-sm">
        Featured
      </div>
    )}
  </div>

  <!-- Action indicator -->
  <div class="absolute top-6 right-6 z-10 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
    <div class="w-10 h-10 bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
      <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 p-6 flex flex-col flex-grow">
    <!-- Header spacing for badges (adjust based on hero image) -->
    <div class={heroImage ? "h-2 mb-3" : "h-6 mb-3"}></div>

    <!-- Main content area with consistent spacing -->
    <div class="flex flex-col flex-grow">
      <h3 class="text-xl font-bold text-slate-800 dark:text-slate-100 mb-3 font-heading group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors duration-300 line-clamp-2">
        {title}
      </h3>
      <div class="flex-grow mb-4">
        <p class="text-slate-600 dark:text-slate-300 leading-relaxed text-sm line-clamp-3">
          {description}
        </p>
      </div>
    </div>

    <!-- Enhanced tags - Fixed position at bottom -->
    <div class="flex flex-wrap gap-2 mt-auto pt-2">
      {sortedTags.slice(0, 3).map((tag, tagIndex) => {
        // Only shorten if the tag is longer than 10 characters
        const shouldShorten = tag.length > 10;
        const displayTag = shouldShorten ? tag
          .replace('Payment', '')
          .replace('Spring Boot', 'Spring')
          .replace('REST API', 'REST')
          .trim() : tag;

        return (
          <span
            class="px-2.5 py-1 bg-gradient-to-r from-slate-100 to-blue-50 dark:from-slate-700 dark:to-slate-600 text-slate-700 dark:text-slate-200 text-xs font-semibold rounded-lg border border-slate-200/50 dark:border-slate-600/50 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/50 dark:hover:to-indigo-900/50 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 hover:scale-105 hover:shadow-md"
            style={`animation-delay: ${tagIndex * 100}ms`}
            title={tag !== displayTag ? tag : undefined}
          >
            {displayTag}
          </span>
        );
      })}
      {tags.length > 3 && (
        <span class="px-2.5 py-1 bg-gradient-to-r from-slate-200 to-slate-100 dark:from-slate-600 dark:to-slate-700 text-slate-600 dark:text-slate-300 text-xs font-semibold rounded-lg border border-slate-200/50 dark:border-slate-600/50">
          +{tags.length - 3}
        </span>
      )}
    </div>

    <!-- Hover effect line -->
    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
  </div>
</article>