---
// ScrollAnimations component for managing scroll-triggered animations
---

<script>
  class ScrollAnimationManager {
    private observers: IntersectionObserver[] = [];
    private animatedElements = new Set<Element>();

    constructor() {
      this.initializeScrollAnimations();
      this.initializeParallaxElements();
      this.initializeCounters();
    }

    private initializeScrollAnimations() {
      // Main scroll animation observer
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
              this.triggerAnimation(entry.target);
              this.animatedElements.add(entry.target);
            }
          });
        },
        {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
        }
      );

      // Observe elements with scroll animation classes
      const animatedElements = document.querySelectorAll(
        '.scroll-animate, .fade-in-up, .fade-in-left, .fade-in-right, .slide-in-bottom, .zoom-in, .stagger-children'
      );

      animatedElements.forEach((el) => {
        el.classList.add('scroll-animate-hidden');
        observer.observe(el);
      });

      this.observers.push(observer);
    }

    private initializeParallaxElements() {
      const parallaxElements = document.querySelectorAll('.parallax');
      
      if (parallaxElements.length > 0) {
        const handleScroll = () => {
          const scrolled = window.pageYOffset;
          const parallax = scrolled * 0.5;

          parallaxElements.forEach((element) => {
            const rate = scrolled * -0.3;
            (element as HTMLElement).style.transform = `translateY(${rate}px)`;
          });
        };

        // Throttle scroll events for performance
        let ticking = false;
        const throttledScroll = () => {
          if (!ticking) {
            requestAnimationFrame(() => {
              handleScroll();
              ticking = false;
            });
            ticking = true;
          }
        };

        window.addEventListener('scroll', throttledScroll, { passive: true });
      }
    }

    private initializeCounters() {
      const counterElements = document.querySelectorAll('.count-up');
      
      const counterObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              this.animateCounter(entry.target as HTMLElement);
              counterObserver.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.7 }
      );

      counterElements.forEach((el) => counterObserver.observe(el));
      this.observers.push(counterObserver);
    }

    private triggerAnimation(element: Element) {
      const htmlElement = element as HTMLElement;
      
      // Remove hidden class and add visible class
      htmlElement.classList.remove('scroll-animate-hidden');
      htmlElement.classList.add('scroll-animate-visible');

      // Handle stagger animations for children
      if (htmlElement.classList.contains('stagger-children')) {
        const children = htmlElement.children;
        Array.from(children).forEach((child, index) => {
          setTimeout(() => {
            child.classList.add('stagger-item-visible');
          }, index * 150);
        });
      }

      // Add reveal animation based on element type
      if (htmlElement.classList.contains('fade-in-up')) {
        htmlElement.style.animation = 'fadeInUp 0.8s ease-out forwards';
      } else if (htmlElement.classList.contains('fade-in-left')) {
        htmlElement.style.animation = 'fadeInLeft 0.8s ease-out forwards';
      } else if (htmlElement.classList.contains('fade-in-right')) {
        htmlElement.style.animation = 'fadeInRight 0.8s ease-out forwards';
      } else if (htmlElement.classList.contains('slide-in-bottom')) {
        htmlElement.style.animation = 'slideInBottom 0.8s ease-out forwards';
      } else if (htmlElement.classList.contains('zoom-in')) {
        htmlElement.style.animation = 'zoomIn 0.6s ease-out forwards';
      } else {
        htmlElement.style.animation = 'fadeInUp 0.8s ease-out forwards';
      }
    }

    private animateCounter(element: HTMLElement) {
      const target = parseInt(element.getAttribute('data-count') || '0');
      const duration = parseInt(element.getAttribute('data-duration') || '2000');
      const increment = target / (duration / 16);
      let current = 0;

      const updateCounter = () => {
        current += increment;
        if (current < target) {
          element.textContent = Math.floor(current).toString();
          requestAnimationFrame(updateCounter);
        } else {
          element.textContent = target.toString();
        }
      };

      updateCounter();
    }

    public destroy() {
      this.observers.forEach(observer => observer.disconnect());
      this.observers = [];
      this.animatedElements.clear();
    }
  }

  // Initialize when DOM is ready
  function initScrollAnimations() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (!prefersReducedMotion) {
      new ScrollAnimationManager();
    } else {
      // For users who prefer reduced motion, just make everything visible
      const hiddenElements = document.querySelectorAll('.scroll-animate-hidden');
      hiddenElements.forEach(el => {
        el.classList.remove('scroll-animate-hidden');
        el.classList.add('scroll-animate-visible');
      });
    }
  }

  // Initialize on load and on navigation (for SPA-like behavior)
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initScrollAnimations);
  } else {
    initScrollAnimations();
  }

  // Re-initialize on page navigation (for Astro view transitions)
  document.addEventListener('astro:page-load', initScrollAnimations);
</script>

<style>
  /* Base scroll animation styles */
  .scroll-animate-hidden {
    opacity: 0;
    visibility: hidden;
  }

  .scroll-animate-visible {
    opacity: 1;
    visibility: visible;
  }

  /* Fade in up animation */
  .fade-in-up.scroll-animate-hidden {
    transform: translateY(30px);
    transition: all 0.8s ease-out;
  }

  .fade-in-up.scroll-animate-visible {
    transform: translateY(0);
  }

  /* Fade in from left */
  .fade-in-left.scroll-animate-hidden {
    transform: translateX(-50px);
    transition: all 0.8s ease-out;
  }

  .fade-in-left.scroll-animate-visible {
    transform: translateX(0);
  }

  /* Fade in from right */
  .fade-in-right.scroll-animate-hidden {
    transform: translateX(50px);
    transition: all 0.8s ease-out;
  }

  .fade-in-right.scroll-animate-visible {
    transform: translateX(0);
  }

  /* Slide in from bottom */
  .slide-in-bottom.scroll-animate-hidden {
    transform: translateY(50px);
    transition: all 0.8s ease-out;
  }

  .slide-in-bottom.scroll-animate-visible {
    transform: translateY(0);
  }

  /* Zoom in */
  .zoom-in.scroll-animate-hidden {
    transform: scale(0.8);
    transition: all 0.6s ease-out;
  }

  .zoom-in.scroll-animate-visible {
    transform: scale(1);
  }

  /* Stagger children animations */
  .stagger-children > * {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }

  .stagger-children > .stagger-item-visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Parallax elements */
  .parallax {
    will-change: transform;
  }

  /* Counter animations */
  .count-up {
    font-variant-numeric: tabular-nums;
  }

  /* Keyframe animations for fallback */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInBottom {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Performance optimizations */
  .scroll-animate-hidden,
  .scroll-animate-visible {
    will-change: transform, opacity;
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .scroll-animate-hidden,
    .scroll-animate-visible,
    .fade-in-up,
    .fade-in-left,
    .fade-in-right,
    .slide-in-bottom,
    .zoom-in,
    .stagger-children > * {
      transition: none !important;
      animation: none !important;
      transform: none !important;
      opacity: 1 !important;
      visibility: visible !important;
    }
  }
</style>