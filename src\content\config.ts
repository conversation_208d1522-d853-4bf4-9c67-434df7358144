import { defineCollection, z } from 'astro:content';

const portfolioCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    publishDate: z.date(),
    problem: z.string(),
    solution: z.string(),
    technologies: z.array(z.string()),
    role: z.string(),
    results: z.string(),
    repoUrl: z.string().url().optional(),
    liveUrl: z.string().url().optional(),
    heroImage: z.string(),
    // Enhanced image gallery support
    gallery: z.array(z.object({
      src: z.string(),
      alt: z.string(),
      caption: z.string().optional(),
      category: z.enum(['screenshot', 'diagram', 'demo', 'architecture', 'ui', 'mobile']).optional(),
    })).optional(),
    // Enhanced project metadata
    projectType: z.enum(['web-app', 'mobile-app', 'api', 'system', 'platform', 'tool']).optional(),
    status: z.enum(['completed', 'in-progress', 'archived']).default('completed'),
    featured: z.boolean().default(false),
    duration: z.string().optional(),
    teamSize: z.string().optional(),
    // Enhanced content sections
    highlights: z.array(z.string()).optional(),
    challenges: z.array(z.string()).optional(),
    learnings: z.array(z.string()).optional(),
    // Technical specifications
    architecture: z.object({
      overview: z.string().optional(),
      components: z.array(z.string()).optional(),
      patterns: z.array(z.string()).optional(),
    }).optional(),
    performance: z.object({
      metrics: z.array(z.object({
        label: z.string(),
        value: z.string(),
        description: z.string().optional(),
      })).optional(),
      optimizations: z.array(z.string()).optional(),
    }).optional(),
  }),
});

const resourcesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    url: z.string().url(),
    description: z.string(),
    category: z.string(),
    tags: z.array(z.string()),
  }),
});

const aboutCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    updatedDate: z.date(),
    sections: z.array(z.object({
      heading: z.string(),
      content: z.string().optional(),
      subsections: z.array(z.object({
        subheading: z.string().optional(),
        content: z.string().optional(),
        items: z.array(z.string()).optional(),
      })).optional(),
    })),
  }),
});

const homepageCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    lastUpdated: z.date(),
    hero: z.object({
      headline: z.string(),
      subheadline: z.string(),
      description: z.string(),
      highlights: z.array(z.object({
        icon: z.string(),
        label: z.string(),
      })),
      primaryCTA: z.object({
        text: z.string(),
        url: z.string(),
      }),
      secondaryCTA: z.object({
        text: z.string(),
        url: z.string(),
      }),
    }),
    about: z.object({
      openingLine: z.string(),
      mainContent: z.array(z.string()),
      experienceHighlights: z.array(z.string()),
      skills: z.array(z.object({
        category: z.string(),
        items: z.array(z.string()),
      })),
    }),
    contact: z.object({
      social: z.object({
        linkedin: z.string().url(),
        github: z.string().url(),
      }),
      responseTime: z.string(),
      introText: z.string(),
    }),
  }),
});

export const collections = {
  'portfolio': portfolioCollection,
  'resources': resourcesCollection,
  'about': aboutCollection,
  'homepage': homepageCollection,
};