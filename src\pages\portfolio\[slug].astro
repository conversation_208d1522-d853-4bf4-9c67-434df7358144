---
import Layout from '../../layouts/Layout.astro';
import JsonLd from '../../components/JsonLd.astro';
import ImageGallery from '../../components/ImageGallery.astro';
import { getCollection, type CollectionEntry } from 'astro:content';
import { formatTechName, getTechBadgeClasses, sortTechnologies, getTechIcon } from '../../utils/techUtils.js';

export async function getStaticPaths() {
  const projects = await getCollection('portfolio');
  return projects.map(project => ({
    params: { slug: project.slug },
    props: { project },
  }));
}

interface Props {
  project: CollectionEntry<'portfolio'>;
}

const { project } = Astro.props;
const { Content } = await project.render();

// Prepare Article JSON-LD data
const articleData = {
  title: project.data.title,
  description: project.data.problem,
  datePublished: project.data.publishDate.toISOString(),
  dateModified: project.data.publishDate.toISOString(),
  image: project.data.heroImage ? `https://nobhokleng.dev/${project.data.heroImage}` : 'https://nobhokleng.dev/headshot.jpg',
  url: `https://nobhokleng.dev/portfolio/${project.slug}`,
  keywords: project.data.technologies
};
---

<Layout
  title={project.data.title + ' | Portfolio | Nob Hokleng'}
  description={project.data.problem}
  ogType="article"
  keywords={`${project.data.technologies.join(', ')}, portfolio, project, ${project.data.title}`}
  ogImage={project.data.heroImage ? `https://nobhokleng.dev/${project.data.heroImage}` : 'https://nobhokleng.dev/headshot.jpg'}
>
  <JsonLd type="article" data={articleData} />
  <article class="pt-32 pb-24 bg-white">
    <div class="container mx-auto px-5 max-w-4xl">
      <!-- Project Header -->
      <header class="mb-12">
        <div class="mb-6">
          <a href="/portfolio" class="inline-flex items-center text-primary font-medium hover:text-secondary transition-colors">
            <i class="fas fa-arrow-left mr-2"></i> Back to Portfolio
          </a>
        </div>
        
        <h1 class="text-4xl md:text-5xl font-bold text-secondary mb-6 font-heading">{project.data.title}</h1>
        
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-8">
          <span class="flex items-center">
            <i class="fas fa-calendar mr-2"></i>
            {project.data.publishDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </span>
          <span class="flex items-center">
            <i class="fas fa-user mr-2"></i>
            {project.data.role}
          </span>
          {project.data.duration && (
            <span class="flex items-center">
              <i class="fas fa-clock mr-2"></i>
              {project.data.duration}
            </span>
          )}
          {project.data.teamSize && (
            <span class="flex items-center">
              <i class="fas fa-users mr-2"></i>
              {project.data.teamSize}
            </span>
          )}
          {project.data.projectType && (
            <span class="flex items-center">
              <i class="fas fa-tag mr-2"></i>
              {project.data.projectType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
          )}
        </div>

        <div class="flex flex-wrap gap-3 mb-8 tech-badges-container min-h-[3rem] items-start">
          {sortTechnologies(project.data.technologies).map((tech) => {
            const displayTech = formatTechName(tech);
            const icon = getTechIcon(tech);
            return (
              <span
                class={getTechBadgeClasses(tech)}
                title={tech !== displayTech ? tech : undefined}
              >
                {icon && <span class="mr-1 text-sm">{icon}</span>}
                {displayTech}
              </span>
            );
          })}
        </div>
      </header>

      <!-- Project Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
            Problem
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.problem}</p>
        </div>
        
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            Solution
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.solution}</p>
        </div>
        
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-chart-line text-green-500 mr-2"></i>
            Results
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.results}</p>
        </div>
      </div>

      <!-- Project Links -->
      {(project.data.repoUrl || project.data.liveUrl) && (
        <div class="flex flex-wrap gap-4 mb-12">
          {project.data.repoUrl && (
            <a href={project.data.repoUrl} target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <i class="fab fa-github"></i>
              View Repository
            </a>
          )}
          {project.data.liveUrl && (
            <a href={project.data.liveUrl} target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center gap-2 px-6 py-3 border-2 border-primary text-primary rounded-lg font-semibold hover:bg-primary/5 transition-all duration-300 hover:-translate-y-1">
              <i class="fas fa-external-link-alt"></i>
              Live Demo
            </a>
          )}
        </div>
      )}

      <!-- Image Gallery -->
      {project.data.gallery && project.data.gallery.length > 0 && (
        <div class="mb-12">
          <ImageGallery
            images={project.data.gallery}
            title="Project Gallery"
            className="mb-8"
          />
        </div>
      )}

      <!-- Project Highlights -->
      {project.data.highlights && project.data.highlights.length > 0 && (
        <div class="mb-12">
          <h3 class="text-2xl font-bold text-secondary mb-6 font-heading flex items-center">
            <i class="fas fa-star text-yellow-500 mr-3"></i>
            Key Highlights
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {project.data.highlights.map((highlight) => (
              <div class="flex items-start p-4 bg-light rounded-lg">
                <i class="fas fa-check-circle text-green-500 mr-3 mt-1 flex-shrink-0"></i>
                <span class="text-text">{highlight}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <!-- Technical Architecture -->
      {project.data.architecture && (
        <div class="mb-12">
          <h3 class="text-2xl font-bold text-secondary mb-6 font-heading flex items-center">
            <i class="fas fa-sitemap text-blue-500 mr-3"></i>
            Architecture & Design
          </h3>
          <div class="bg-light p-6 rounded-2xl">
            {project.data.architecture.overview && (
              <div class="mb-6">
                <h4 class="text-lg font-semibold text-primary mb-3">Overview</h4>
                <p class="text-text leading-relaxed">{project.data.architecture.overview}</p>
              </div>
            )}

            {project.data.architecture.components && project.data.architecture.components.length > 0 && (
              <div class="mb-6">
                <h4 class="text-lg font-semibold text-primary mb-3">Key Components</h4>
                <ul class="space-y-2">
                  {project.data.architecture.components.map((component) => (
                    <li class="flex items-center text-text">
                      <i class="fas fa-cube text-blue-500 mr-3"></i>
                      {component}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {project.data.architecture.patterns && project.data.architecture.patterns.length > 0 && (
              <div>
                <h4 class="text-lg font-semibold text-primary mb-3">Design Patterns</h4>
                <div class="flex flex-wrap gap-2">
                  {project.data.architecture.patterns.map((pattern) => (
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <!-- Performance Metrics -->
      {project.data.performance && (
        <div class="mb-12">
          <h3 class="text-2xl font-bold text-secondary mb-6 font-heading flex items-center">
            <i class="fas fa-tachometer-alt text-green-500 mr-3"></i>
            Performance & Metrics
          </h3>
          <div class="bg-light p-6 rounded-2xl">
            {project.data.performance.metrics && project.data.performance.metrics.length > 0 && (
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {project.data.performance.metrics.map((metric) => (
                  <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                    <div class="text-2xl font-bold text-primary mb-2">{metric.value}</div>
                    <div class="text-sm font-semibold text-secondary mb-1">{metric.label}</div>
                    {metric.description && (
                      <div class="text-xs text-gray-600">{metric.description}</div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {project.data.performance.optimizations && project.data.performance.optimizations.length > 0 && (
              <div>
                <h4 class="text-lg font-semibold text-primary mb-3">Optimizations</h4>
                <ul class="space-y-2">
                  {project.data.performance.optimizations.map((optimization) => (
                    <li class="flex items-center text-text">
                      <i class="fas fa-rocket text-green-500 mr-3"></i>
                      {optimization}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      <!-- Challenges & Learnings -->
      {(project.data.challenges || project.data.learnings) && (
        <div class="mb-12">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {project.data.challenges && project.data.challenges.length > 0 && (
              <div>
                <h3 class="text-xl font-bold text-secondary mb-4 font-heading flex items-center">
                  <i class="fas fa-exclamation-triangle text-orange-500 mr-3"></i>
                  Challenges
                </h3>
                <ul class="space-y-3">
                  {project.data.challenges.map((challenge) => (
                    <li class="flex items-start p-3 bg-orange-50 rounded-lg">
                      <i class="fas fa-arrow-right text-orange-500 mr-3 mt-1 flex-shrink-0"></i>
                      <span class="text-text text-sm">{challenge}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {project.data.learnings && project.data.learnings.length > 0 && (
              <div>
                <h3 class="text-xl font-bold text-secondary mb-4 font-heading flex items-center">
                  <i class="fas fa-lightbulb text-yellow-500 mr-3"></i>
                  Key Learnings
                </h3>
                <ul class="space-y-3">
                  {project.data.learnings.map((learning) => (
                    <li class="flex items-start p-3 bg-yellow-50 rounded-lg">
                      <i class="fas fa-arrow-right text-yellow-500 mr-3 mt-1 flex-shrink-0"></i>
                      <span class="text-text text-sm">{learning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      <!-- Project Content -->
      <div class="prose prose-lg max-w-none prose-headings:font-heading prose-headings:text-secondary prose-a:text-primary prose-strong:text-secondary">
        <Content />
      </div>

      <!-- Navigation -->
      <div class="mt-16 pt-8 border-t border-gray-200">
        <div class="flex justify-center">
          <a href="/portfolio" class="inline-flex items-center gap-2 px-6 py-3 bg-light text-secondary rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300">
            <i class="fas fa-arrow-left"></i>
            Back to Portfolio
          </a>
        </div>
      </div>
    </div>
  </article>
</Layout> 