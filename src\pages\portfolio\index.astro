---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import ProjectCard from '../../components/ProjectCard.astro';
import { formatTechName, getTechBadgeClasses, sortTechnologies } from '../../utils/techUtils.js';

const projects = await getCollection('portfolio');
---

<Layout title="Portfolio | Nob Hokleng | Software Developer & System Architect">
  <section class="portfolio pt-32 pb-24 bg-light" role="region" aria-labelledby="portfolio-title">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 id="portfolio-title" class="text-4xl font-bold text-center mb-12 font-heading relative">
        Portfolio
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <!-- Portfolio Introduction -->
      <div class="text-center mb-16">
        <p class="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
          Explore my professional journey through these carefully selected projects that showcase
          technical expertise, problem-solving capabilities, and innovative solutions across various domains.
        </p>
      </div>

      <!-- Featured Projects Section -->
      {projects.filter(p => p.data.featured).length > 0 && (
        <div class="mb-20">
          <div class="flex items-center gap-3 mb-8">
            <div class="w-1 h-8 bg-gradient-to-b from-primary to-accent rounded-full"></div>
            <h2 class="text-2xl font-bold text-secondary font-heading">Featured Projects</h2>
            <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12 lg:items-stretch">
            {projects.filter(p => p.data.featured).slice(0, 2).map((project) => (
              <div class="portfolio-item featured-item" role="listitem">
                <a href={`/portfolio/${project.slug}`} class="block group h-full">
                  <div class="relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 h-full flex flex-col min-h-[500px] lg:min-h-[550px]">
                    {project.data.heroImage && (
                      <div class="relative h-64 overflow-hidden flex-shrink-0">
                        <img
                          src={project.data.heroImage}
                          alt={`${project.data.title} preview`}
                          class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                          loading="lazy"
                        />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                        <div class="absolute top-4 left-4">
                          <span class="px-3 py-1 bg-yellow-400 text-yellow-900 text-xs font-bold rounded-full shadow-lg">
                            Featured
                          </span>
                        </div>
                      </div>
                    )}

                    <div class="p-8 flex flex-col flex-grow">
                      <h3 class="text-2xl font-bold text-secondary mb-4 font-heading group-hover:text-primary transition-colors">
                        {project.data.title}
                      </h3>
                      <p class="text-slate-600 leading-relaxed mb-6 line-clamp-3 flex-grow">
                        {project.data.problem}
                      </p>

                      <div class="flex flex-wrap gap-2 mb-6 tech-badges-container" style="max-height: 4.5rem; overflow: hidden;">
                        {sortTechnologies(project.data.technologies).slice(0, 6).map((tech) => {
                          const displayTech = formatTechName(tech);

                          return (
                            <span
                              class={getTechBadgeClasses(tech)}
                              title={tech !== displayTech ? tech : undefined}
                            >
                              {displayTech}
                            </span>
                          );
                        })}
                      </div>

                      <div class="flex items-center justify-between mt-auto">
                        <span class="text-sm text-slate-500">
                          {project.data.publishDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })}
                        </span>
                        <div class="flex items-center text-primary font-medium group-hover:translate-x-1 transition-transform">
                          View Project
                          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      <!-- All Projects Section -->
      <div class="flex items-center gap-3 mb-8">
        <div class="w-1 h-8 bg-gradient-to-b from-secondary to-primary rounded-full"></div>
        <h2 class="text-2xl font-bold text-secondary font-heading">All Projects</h2>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
      </div>

      <!-- Consistent Grid Layout -->
      <div class="portfolio-grid">
        {projects.map((project) => {
          const isFeatured = project.data.featured;

          return (
            <div
              class={`portfolio-item ${isFeatured ? 'featured-border' : ''}`}
              role="listitem"
            >
              <a href={`/portfolio/${project.slug}`} class="block group h-full">
                <div class="relative h-full overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-1 border border-slate-200/50 flex flex-col min-h-[400px]">
                  {project.data.heroImage && (
                    <div class="relative overflow-hidden h-48">
                      <img
                        src={project.data.heroImage}
                        alt={`${project.data.title} preview`}
                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        loading="lazy"
                      />
                      <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>

                      {isFeatured && (
                        <div class="absolute top-3 left-3">
                          <span class="px-3 py-1 bg-yellow-400 text-yellow-900 text-xs font-bold rounded-full shadow-lg">
                            Featured
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  <div class="p-6 flex flex-col flex-1">
                    <h3 class="text-xl font-bold text-secondary mb-3 font-heading group-hover:text-primary transition-colors line-clamp-2">
                      {project.data.title}
                    </h3>

                    <p class="text-slate-600 leading-relaxed mb-4 flex-1 line-clamp-3">
                      {project.data.problem}
                    </p>

                    <div class="flex flex-wrap gap-2 mb-4 tech-badges-container" style="max-height: 3.5rem; overflow: hidden;">
                      {sortTechnologies(project.data.technologies).slice(0, 6).map((tech) => {
                        const displayTech = formatTechName(tech);

                        return (
                          <span
                            class={getTechBadgeClasses(tech)}
                            title={tech !== displayTech ? tech : undefined}
                          >
                            {displayTech}
                          </span>
                        );
                      })}
                      {project.data.technologies.length > 6 && (
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-slate-100 to-gray-100 text-slate-600 border border-slate-200/50 backdrop-blur-sm tech-badge-enhanced">
                          +{project.data.technologies.length - 6}
                        </span>
                      )}
                    </div>

                    <div class="flex items-center justify-between mt-auto pt-2 border-t border-slate-100">
                      <span class="text-sm text-slate-500 font-medium">
                        {project.data.publishDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })}
                      </span>
                      <div class="flex items-center text-primary text-sm font-semibold group-hover:translate-x-1 transition-transform">
                        View Project
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          );
        })}
      </div>
    </div>
  </section>
</Layout>

<script>
  // Modern portfolio interactions
  document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation to portfolio items
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add('animate-in');
          }, index * 100);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    portfolioItems.forEach(item => {
      observer.observe(item);
    });
  });
</script>

<style>
  /* Modern Consistent Grid Layout */
  .portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    align-items: start;
  }

  /* Featured items get special styling */
  .featured-border {
    position: relative;
  }

  .featured-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #f59e0b);
    border-radius: 1rem;
    z-index: -1;
    opacity: 0.7;
  }

  /* Animation classes */
  .portfolio-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 400px;
  }

  .portfolio-item.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Ensure consistent card heights */
  .portfolio-item .relative.h-full {
    min-height: 400px;
  }

  /* Featured items styling */
  .featured-item {
    opacity: 1;
    transform: none;
  }

  /* Hover effects */
  .portfolio-item:hover {
    transform: translateY(-4px);
  }

  .featured-item:hover {
    transform: translateY(-8px);
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .portfolio-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .featured-item .relative.h-64 {
      height: 12rem;
    }

    /* Featured projects responsive */
    .featured-item .min-h-\[500px\] {
      min-height: 400px;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .portfolio-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    /* Featured projects tablet */
    .featured-item .min-h-\[500px\] {
      min-height: 450px;
    }
  }

  @media (min-width: 1025px) {
    .portfolio-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }
  }

  @media (min-width: 1400px) {
    .portfolio-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }
  }

  /* Smooth gradient animations */
  .featured-border::before {
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% {
      background: linear-gradient(45deg, #3b82f6, #8b5cf6, #f59e0b);
    }
    50% {
      background: linear-gradient(45deg, #f59e0b, #3b82f6, #8b5cf6);
    }
  }
</style>