@tailwind base;
@tailwind components;
@tailwind utilities;

/* Line clamp utilities for consistent text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Base styles for layout structure */
body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2b2d42;
  line-height: 1.6;
  background-color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles */
.dark body {
  color: #e2e8f0;
  background-color: #0f172a;
}

main {
  flex-grow: 1;
}

/* Custom scrolling for smooth navigation */
html {
  scroll-behavior: smooth;
}

/* Animation keyframes */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(255, 158, 0, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.03);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(2deg);
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce-delayed {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Utility Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-bounce-slow {
  animation: bounce-slow 3s ease-in-out infinite;
}

.animate-bounce-delayed {
  animation: bounce-delayed 4s ease-in-out infinite;
  animation-delay: 1.5s;
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
  opacity: 0;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out forwards;
  opacity: 0;
}

/* Modern Animation Keyframes */
@keyframes gradient-x {
  0%, 100% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(100%);
  }
}

@keyframes gradient-y {
  0%, 100% {
    transform: translateY(0%);
  }
  50% {
    transform: translateY(100%);
  }
}

@keyframes gradient-xy {
  0%, 100% {
    transform: translate(0%, 0%);
  }
  25% {
    transform: translate(100%, 0%);
  }
  50% {
    transform: translate(100%, 100%);
  }
  75% {
    transform: translate(0%, 100%);
  }
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

/* Glassmorphism utility classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-effect-dark {
  background: rgba(15, 23, 42, 0.25);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-card {
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.1),
    rgba(15, 23, 42, 0.05)
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Magnetic cursor effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.magnetic:hover {
  transform: scale(1.05);
}

/* Modern scroll snap */
.scroll-snap-x {
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
}

.scroll-snap-item {
  scroll-snap-align: start;
}

/* Enhanced focus states */
.focus-modern:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  border-radius: 0.5rem;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-float,
  .animate-float-delayed,
  .animate-float-slow,
  .animate-bounce-slow,
  .animate-bounce-delayed,
  .animate-gradient-x,
  .animate-gradient-y,
  .animate-gradient-xy,
  .animate-float-gentle,
  .animate-pulse-glow,
  .animate-shimmer {
    animation: none !important;
  }
}

/* Animation Delay Classes */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-1200 {
  animation-delay: 1.2s;
}