/* Modern Technology Badge Styles */

/* Enhanced glassmorphism effects for tech badges */
.tech-badge-enhanced {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 1.4rem;
  line-height: 1;
  white-space: nowrap;
  font-size: 0.7rem;
  max-width: fit-content;
}

.tech-badge-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s ease;
}

.tech-badge-enhanced:hover::before {
  left: 100%;
}

/* Staggered animation for badge containers */
.tech-badges-container {
  animation: fadeInUp 0.6s ease-out;
  align-items: flex-start;
  align-content: flex-start;
}

.tech-badges-container .tech-badge-enhanced {
  animation: slideInScale 0.4s ease-out backwards;
  flex-shrink: 0;
}

.tech-badges-container .tech-badge-enhanced:nth-child(1) {
  animation-delay: 0.1s;
}

.tech-badges-container .tech-badge-enhanced:nth-child(2) {
  animation-delay: 0.2s;
}

.tech-badges-container .tech-badge-enhanced:nth-child(3) {
  animation-delay: 0.3s;
}

.tech-badges-container .tech-badge-enhanced:nth-child(4) {
  animation-delay: 0.4s;
}

.tech-badges-container .tech-badge-enhanced:nth-child(5) {
  animation-delay: 0.5s;
}

.tech-badges-container .tech-badge-enhanced:nth-child(6) {
  animation-delay: 0.6s;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Enhanced hover effects for different tech categories */
.tech-badge-languages:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tech-badge-frameworks:hover {
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tech-badge-databases:hover {
  box-shadow: 0 8px 25px rgba(147, 51, 234, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tech-badge-cloud:hover {
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tech-badge-tools:hover {
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tech-badge-payment:hover {
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
  transform: translateY(-2px) scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tech-badges-container {
    gap: 0.25rem;
  }

  .tech-badge-enhanced {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
    height: 1.25rem;
    line-height: 1;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .tech-badge-enhanced::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
}

/* Focus states for accessibility */
.tech-badge-enhanced:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .tech-badge-enhanced {
    background: #f3f4f6 !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    box-shadow: none !important;
  }
}
