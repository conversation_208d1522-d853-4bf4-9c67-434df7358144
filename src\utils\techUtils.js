/**
 * Technology name processing utilities for portfolio components
 * Provides consistent tech name formatting and categorization
 */

// Comprehensive technology name mappings
const TECH_MAPPINGS = {
  // Frameworks & Libraries
  'Spring Boot': 'Spring',
  'REST API': 'REST',
  'Microservices': 'Micro',
  'Micronaut': 'Micro',
  'React Native': 'RN',
  'Node.js': 'Node',
  'Express.js': 'Express',
  'Next.js': 'Next',
  'Vue.js': 'Vue',
  'Angular.js': 'Angular',
  
  // Databases
  'PostgreSQL': 'Postgres',
  'MongoDB': 'Mongo',
  'Elasticsearch': 'Elastic',
  
  // Cloud & DevOps
  'Amazon Web Services': 'AWS',
  'Google Cloud Platform': 'GCP',
  'Microsoft Azure': 'Azure',
  'Docker Compose': 'Docker',
  'Kubernetes': 'K8s',
  
  // Payment Systems
  'ABA Payment': 'ABA',
  'Wing Payment': 'Wing',
  'Cellcard Payment': 'Cellcard',
  'PayPal Integration': 'PayPal',
  'Stripe Payment': 'Stripe',
  'Payment Gateway': 'Payment',
  'Payment Integration': 'Payment',
  
  // Tools & Platforms
  'Visual Studio Code': 'VS Code',
  'IntelliJ IDEA': 'IntelliJ',
  'GitHub Actions': 'GH Actions',
  'Jenkins CI/CD': 'Jenkins',
  'Monitoring': 'Monitor',
  'System Monitoring': 'Monitor',
  
  // Languages (keep as-is but define for consistency)
  'JavaScript': 'JavaScript',
  'TypeScript': 'TypeScript',
  'Python': 'Python',
  'Java': 'Java',
  'C#': 'C#',
  'PHP': 'PHP',
  'Go': 'Go',
  'Rust': 'Rust',
  'Swift': 'Swift',
  'Kotlin': 'Kotlin'
};

// Technology categories for styling
const TECH_CATEGORIES = {
  languages: ['Java', 'JavaScript', 'TypeScript', 'Python', 'C#', 'PHP', 'Go', 'Rust', 'Swift', 'Kotlin', 'Groovy'],
  frameworks: ['Spring', 'React', 'Vue', 'Angular', 'Next', 'Express', 'Django', 'Laravel', 'Grails', 'Micronaut'],
  databases: ['MySQL', 'Postgres', 'Mongo', 'Redis', 'Elastic', 'SQLite', 'Oracle'],
  cloud: ['AWS', 'GCP', 'Azure', 'Docker', 'K8s', 'Heroku', 'Vercel', 'Netlify'],
  tools: ['Git', 'VS Code', 'IntelliJ', 'Jenkins', 'GitHub', 'Jira', 'Monit'],
  payment: ['ABA', 'Wing', 'Cellcard', 'PayPal', 'Stripe'],
  other: ['REST', 'JWT', 'Micro', 'API', 'GraphQL', 'WebSocket']
};

/**
 * Format technology name for display
 * @param {string} tech - Original technology name
 * @param {number} maxLength - Maximum length for fallback truncation (default: 10)
 * @returns {string} Formatted technology name
 */
export function formatTechName(tech, maxLength = 10) {
  if (!tech) return '';
  
  // Check if we have a predefined mapping
  if (TECH_MAPPINGS[tech]) {
    return TECH_MAPPINGS[tech];
  }
  
  // Intelligent fallback truncation
  if (tech.length <= maxLength) {
    return tech;
  }
  
  // Try to find a good truncation point
  const words = tech.split(' ');
  if (words.length > 1) {
    // For multi-word technologies, try to use first word or abbreviation
    const firstWord = words[0];
    if (firstWord.length <= maxLength) {
      return firstWord;
    }
    
    // Create abbreviation from first letters
    const abbreviation = words.map(word => word.charAt(0).toUpperCase()).join('');
    if (abbreviation.length <= maxLength) {
      return abbreviation;
    }
  }
  
  // Last resort: simple truncation with ellipsis
  return tech.substring(0, maxLength - 2) + '..';
}

/**
 * Get technology category for styling purposes
 * @param {string} tech - Technology name (formatted or original)
 * @returns {string} Category name
 */
export function getTechCategory(tech) {
  const formattedTech = formatTechName(tech);
  
  for (const [category, techs] of Object.entries(TECH_CATEGORIES)) {
    if (techs.includes(formattedTech) || techs.includes(tech)) {
      return category;
    }
  }
  
  return 'other';
}

/**
 * Sort technologies for optimal display
 * Prioritizes important/common technologies and shorter names
 * @param {string[]} technologies - Array of technology names
 * @returns {string[]} Sorted array of technology names
 */
export function sortTechnologies(technologies) {
  return [...technologies].sort((a, b) => {
    const aFormatted = formatTechName(a);
    const bFormatted = formatTechName(b);
    const aCategory = getTechCategory(a);
    const bCategory = getTechCategory(b);
    
    // Priority order: languages > frameworks > databases > cloud > tools > payment > other
    const categoryPriority = {
      languages: 1,
      frameworks: 2,
      databases: 3,
      cloud: 4,
      tools: 5,
      payment: 6,
      other: 7
    };
    
    const aPriority = categoryPriority[aCategory] || 7;
    const bPriority = categoryPriority[bCategory] || 7;
    
    // First sort by category priority
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }
    
    // Then by formatted length (shorter first)
    if (aFormatted.length !== bFormatted.length) {
      return aFormatted.length - bFormatted.length;
    }
    
    // Finally alphabetically
    return aFormatted.localeCompare(bFormatted);
  });
}

/**
 * Get technology icon (emoji or symbol) for popular technologies
 * @param {string} tech - Technology name
 * @returns {string} Icon/emoji for the technology
 */
export function getTechIcon(tech) {
  const iconMap = {
    // Languages
    'Java': '☕',
    'JavaScript': '🟨',
    'TypeScript': '🔷',
    'Python': '🐍',
    'PHP': '🐘',
    'Go': '🐹',
    'Rust': '🦀',
    'Swift': '🍎',

    // Frameworks
    'Spring': '🍃',
    'React': '⚛️',
    'Vue': '💚',
    'Angular': '🅰️',
    'Next': '▲',

    // Databases
    'MySQL': '🐬',
    'Postgres': '🐘',
    'Mongo': '🍃',
    'Redis': '🔴',
    'Elastic': '🔍',

    // Cloud & DevOps
    'AWS': '☁️',
    'Docker': '🐳',
    'K8s': '⚙️',
    'Azure': '☁️',
    'GCP': '☁️',

    // Tools
    'Git': '📝',
    'GitHub': '🐙',
    'VS Code': '💻',

    // Other
    'REST': '🌐',
    'API': '🔌',
    'JWT': '🔐'
  };

  return iconMap[formatTechName(tech)] || '';
}

/**
 * Get CSS classes for technology badge based on category
 * @param {string} tech - Technology name
 * @param {boolean} enhanced - Whether to include enhanced styling classes
 * @returns {string} CSS classes for styling
 */
export function getTechBadgeClasses(tech, enhanced = true) {
  const category = getTechCategory(tech);

  const baseClasses = 'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium transition-all duration-300 hover:scale-105 hover:shadow-md backdrop-blur-sm border whitespace-nowrap';
  const enhancedClasses = enhanced ? 'tech-badge-enhanced' : '';

  const categoryClasses = {
    languages: `bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200/50 hover:from-blue-100 hover:to-indigo-100 hover:text-blue-800 hover:border-blue-300 ${enhanced ? 'tech-badge-languages' : ''}`,
    frameworks: `bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200/50 hover:from-green-100 hover:to-emerald-100 hover:text-green-800 hover:border-green-300 ${enhanced ? 'tech-badge-frameworks' : ''}`,
    databases: `bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 border-purple-200/50 hover:from-purple-100 hover:to-violet-100 hover:text-purple-800 hover:border-purple-300 ${enhanced ? 'tech-badge-databases' : ''}`,
    cloud: `bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200/50 hover:from-orange-100 hover:to-amber-100 hover:text-orange-800 hover:border-orange-300 ${enhanced ? 'tech-badge-cloud' : ''}`,
    tools: `bg-gradient-to-r from-gray-50 to-slate-50 text-gray-700 border-gray-200/50 hover:from-gray-100 hover:to-slate-100 hover:text-gray-800 hover:border-gray-300 ${enhanced ? 'tech-badge-tools' : ''}`,
    payment: `bg-gradient-to-r from-pink-50 to-rose-50 text-pink-700 border-pink-200/50 hover:from-pink-100 hover:to-rose-100 hover:text-pink-800 hover:border-pink-300 ${enhanced ? 'tech-badge-payment' : ''}`,
    other: `bg-gradient-to-r from-slate-50 to-gray-50 text-slate-700 border-slate-200/50 hover:from-slate-100 hover:to-gray-100 hover:text-slate-800 hover:border-slate-300`
  };

  return `${baseClasses} ${enhancedClasses} ${categoryClasses[category] || categoryClasses.other}`.trim();
}
